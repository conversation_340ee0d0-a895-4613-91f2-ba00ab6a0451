"""
Django settings for migrateproject project.

Generated by 'django-admin startproject' using Django 3.2.25.

For more information on this file, see
https://docs.djangoproject.com/en/3.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/3.2/ref/settings/
"""
import os
from pathlib import Path

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/3.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'django-insecure-@ie8f)*!sm4mf8m@&&sdcbe(4vj@ttykqvh@er82bcq%gc33m#'

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = False

# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'rest_framework',
    'msboard',
    'django_apscheduler',
    'drf_yasg'  # 接口文档
]

ALLOWED_HOSTS = ['*']

MIDDLEWARE = [
    'django.middleware.common.CommonMiddleware',
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

ROOT_URLCONF = 'migrateproject.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'migrateproject.wsgi.application'

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'a_migrate_project',  # 替换为你的数据库名称
        'USER': 'root',  # 替换为你的 MySQL 用户名
        'PASSWORD': '123.456',  # 替换为你的 MySQL 密码
        'HOST': '***************',  # 如果数据库在本地，可以使用 'localhost'
        # 'HOST': '************',  # 如果数据库在本地，可以使用 'localhost'
        'PORT': '3306',  # MySQL 默认端口是 3306，如果有修改，请更改为相应的端口
    },
}

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

LANGUAGE_CODE = "en-us"

TIME_ZONE = "Asia/Shanghai"

USE_I18N = True

USE_L10N = True

USE_TZ = True

STATIC_URL = '/static/'

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

INTERFACE_BOARD_DATA = None
ENV_MAP = {
    "beta": ["beta环境"],
    "pre": ["预发环境", "新休闲-PRE环境"],
    "gray": ["新休闲-GRAY环境"],
    "prod": ["生产环境", "新饮食-线上环境", "新休闲-PRO环境", "新正餐-PRO环境","供应链-PRO环境","新正餐-本地环境（云）"],
}

CONFIG_PAGE = [
    {"interface_page": [
        {"case_page": "https://qimaitech.feishu.cn/share/base/dashboard/shrcnSIeC35qHW9hHS0lXsGTl2f"},
        {"bug_page": "https://qimaitech.feishu.cn/wiki/YexHwVEH7ia6IrkOMAscQR9Znxc?from=from_copylink"}
    ]},
    {"ui_page": []}
]

CURRENT_TIME = None
