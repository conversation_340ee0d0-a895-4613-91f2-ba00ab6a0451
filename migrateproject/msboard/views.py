import asyncio

from django.shortcuts import get_object_or_404
from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView
from migrateproject import settings
from msboard import tasks, models
from msboard.models import DashboardModel, choices_to_list, AppModel
from msboard.serializers import DashboardSerializer, DashboardBaseSerializer, DashboardModelDeleteSerializer, \
    AppModelSerializer, AppModelDeleteSerializer
from msboard.services.execute_service import Execute
from msboard.services.interface_service import ApiClient, ApiConfig, DashboardCollection, transform_data, \
    dashboard_collection_task


class CreateDashboardView(APIView):
    def post(self, request, format=None):
        serializer = DashboardSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            DashboardCollection.add_record(request.data.get("scene_id"), request.data.get("env"))
            return Response({"code": 200, "message": "创建成功", "data": serializer.data}, status=status.HTTP_200_OK)
        return Response({"code": 400, "message": "创建失败", "errors": serializer.errors},
                        status=status.HTTP_400_BAD_REQUEST)


class InterfaceBoardListView(APIView):
    env_list = [item[0] for item in models.ENV_CHOICES]

    def get(self, request):
        env = request.query_params.get('env')
        if not env:
            return Response({"code": 400, "message": "缺少必要参数env", "data": None},
                            status=status.HTTP_400_BAD_REQUEST)

        if env not in self.env_list:
            return Response({
                "code": 400,
                "message": f"参数env值无效",
                "data": None
            }, status=status.HTTP_400_BAD_REQUEST)

        # tasks.dashboard_collection_task(check_time=True)  # 更新列表数据
        if settings.INTERFACE_BOARD_DATA:
            transformed_data = transform_data(settings.INTERFACE_BOARD_DATA)
            result_data = transformed_data.get(env, [])
            return Response({
                "code": 200,
                "message": "success",
                "data": result_data
            })

        return Response({
            "code": 400,
            "message": "数据初始化中",
        })


class ConfigPageView(APIView):
    scene_type_list = ['0', '1']

    def get(self, request):
        scene_type = request.query_params.get("scene_type")

        if not scene_type:
            return Response({"code": 400, "message": "缺少必要参数scene_type", "data": None},
                            status=status.HTTP_400_BAD_REQUEST)
        if scene_type not in self.scene_type_list:
            return Response({
                "code": 400,
                "message": f"参数scene_type值无效",
                "data": None
            }, status=status.HTTP_400_BAD_REQUEST)
        if scene_type == self.scene_type_list[0]:
            ms_envs = ApiClient(ApiConfig()).get_test_env_list()
            scene_type = choices_to_list(DashboardModel.SCENE_TYPE_CHOICES)
            feishu_report = choices_to_list(DashboardModel.FEISHU_REPORT_CHOICES)
            board_envs = choices_to_list(models.ENV_CHOICES)
            return Response({
                "code": 200,
                "message": "success",
                "data": {
                    "page": settings.CONFIG_PAGE,
                    "ms_env": ms_envs,
                    "scene_type": scene_type,
                    "feishu_report": feishu_report,
                    "board_envs": board_envs
                }
            })


class EditDashboardView(APIView):
    def post(self, request):
        # 根据 scene_id 查找对象
        scene_id = request.data.get("scene_id")
        env = request.data.get("env")
        instance = get_object_or_404(DashboardModel, scene_id=scene_id, env=env)
        # 反序列化并验证数据
        serializer = DashboardBaseSerializer(instance, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            tasks.dashboard_collection_task(check_time=False)  # 更新列表数据
            return Response({"code": 200, "message": "更新成功", "data": serializer.data}, status=status.HTTP_200_OK)
        return Response({"code": 400, "message": serializer.errors, "data": None}, status=status.HTTP_400_BAD_REQUEST)


class RunView(APIView):
    client = ApiClient(ApiConfig())

    def post(self, request):
        scene_id = request.data.get("scene_id")
        env = request.data.get("env")
        # 重复执行判断
        try:
            history_data = self.client.get_new_history_info(scene_id)[0]
        except Exception as e:
            history_data = None
        if history_data is not None:
            status, exec_status = history_data.get("status"), history_data.get("execStatus")
            if status == "PENDING" and exec_status == "RUNNING":
                return Response({
                    "code": 400,
                    "message": "执行失败：请勿重复执行",
                })
        ms_env_id = get_object_or_404(DashboardModel, scene_id=scene_id, env=env).ms_env_id
        steps, scenario_config = self.client.get_scenario_get(scene_id)
        self.client.scenario_run(
            scenario_config=scenario_config,
            steps=steps,
            environment_id=ms_env_id,
            scene_id=scene_id
        )
        tasks.dashboard_collection_task(check_time=False)
        return Response({
            "code": 200,
            "message": "success",
        })


class Delete(APIView):
    def post(self, request):
        serializer = DashboardModelDeleteSerializer(data=request.data)
        if serializer.is_valid():
            scene_id = serializer.validated_data['scene_id']
            env = serializer.validated_data['env']
            try:
                DashboardCollection.remove_record(scene_id, env)
                dashboard_model = DashboardModel.objects.get(scene_id=scene_id, env=env)
                dashboard_model.delete()
                tasks.dashboard_collection_task(check_time=False)
                return Response({"code": 200, "message": "删除成功", "data": serializer.data})
            except DashboardModel.DoesNotExist:
                return Response({"code": 404, "message": "删除失败"}, status=status.HTTP_404_NOT_FOUND)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class BuildCall(APIView):
    """
    负责将自动化执行任务加入到队列
    """

    def post(self, request):
        changeDict = {
            1: "3",  # pro
            5: "1",  # pre
            2: "2",  # pro-gray
            6: "0",  # beta
        }
        app_name = request.data.get("app_name")
        env = request.data.get("env")
        changed_env = changeDict.get(env, None)
        task_type = request.data.get("task_type")
        task_status = request.data.get("task_status")
        if task_status != 5:
            return Response({
                "code": 200,
                "message": "success",
            })
        if changed_env:
            Execute().put_task_queue(app_name, changed_env)  # 加入执行队列
        return Response({
            "code": 200,
            "message": "success",
        })


class ExecuteListView(APIView):
    def get(self, request):
        query_data = AppModel.get_data()
        board_envs = choices_to_list(models.ENV_CHOICES)
        # 用defaultdict聚合数据
        grouped_data = []
        if query_data:
            for obj in query_data:
                grouped_data.append({
                    obj.env: {
                        "app_name": obj.app_name,
                        "scene_ids": DashboardModel.get_names_by_scene_ids(obj.scene_ids, obj.env),
                    }
                })

        return Response({
            "code": 200,
            "message": "success",
            "data": grouped_data,
            "board_envs": board_envs
        })


class AppCreateView(APIView):
    def post(self, request):
        serializer = AppModelSerializer(data=request.data)
        if serializer.is_valid():
            app_name = serializer.validated_data['app_name']
            env = serializer.validated_data['env']
            scene_ids = serializer.validated_data.get('scene_ids', [])

            obj = AppModel.create_or_update_app(app_name=app_name, env=env, scene_ids=scene_ids)

            return Response({
                "message": "success",
                "data": {
                    "app_name": obj.app_name,
                    "env": obj.env,
                    "scene_ids": obj.scene_ids,
                }
            }, status=status.HTTP_200_OK)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class AppDeleteView(APIView):
    def post(self, request):
        serializer = AppModelDeleteSerializer(data=request.data)
        if serializer.is_valid():
            app_name = serializer.validated_data['app_name']
            env = serializer.validated_data['env']

            obj = AppModel.delete_app(app_name=app_name, env=env)

            if obj:
                return Response({
                    "message": "success",
                    "data": {
                        "app_name": obj.app_name,
                        "env": obj.env
                    }
                }, status=status.HTTP_200_OK)
            else:
                return Response({"message": "记录不存在"}, status=status.HTTP_404_NOT_FOUND)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class AppConfigView(APIView):
    def get(self, request):
        env = request.query_params.get('env')
        app_list = AppModel.get_apps_empty(env)
        print(app_list)
        scene_list = Execute.get_scene_list_by_env(env)
        return Response({
            "message": "success",
            "data": {
                "app_name": app_list,
                "scene_list": scene_list
            }
        })


class DeBugView(APIView):
    def get(self, request):
        dashboard_collection_task(True)
        exe = Execute()
        exe.init_task_list()
        print("tasklist:", exe.task_list)
        exe.execute()  # 构建即执行任务
        return Response({"message": "ok"})
