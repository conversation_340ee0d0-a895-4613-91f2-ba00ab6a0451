from django.db.models import Char<PERSON>ield
from django.utils import timezone
from django.db import models
from django.core.exceptions import ValidationError
from collections import defaultdict


def choices_to_list(choices):
    return [{value: key} for key, value in choices]


ENV_CHOICES = [
    ('3', 'prod'),
    ('1', 'pre'),
    ('2', 'gray'),
    ('0', 'beta'),
]


class DashboardModel(models.Model):
    SCENE_TYPE_CHOICES = [
        ('0', '接口自动化'),
        ('1', 'UI自动化'),
    ]

    FEISHU_REPORT_CHOICES = [
        ('0', '抛出报告'),
        ('1', '不抛出报告'),
        ('2', '仅抛出错误报告')
    ]

    name = models.CharField(max_length=255, verbose_name="名称")
    scene_id = models.CharField(max_length=255, verbose_name="场景ID")
    scene_type = models.CharField(max_length=1, choices=SCENE_TYPE_CHOICES, verbose_name="场景类型")
    env = models.CharField(max_length=1, choices=ENV_CHOICES, verbose_name="看板环境")
    ms_env_id = models.CharField(max_length=255, verbose_name="MS平台环境ID")
    feishu_report = models.CharField(max_length=1, choices=FEISHU_REPORT_CHOICES, verbose_name="飞书报告")
    feishu_robot = models.CharField(max_length=255, verbose_name="机器人地址", null=True, blank=True)
    feishu_user = models.JSONField(verbose_name="飞书用户列表", null=True, blank=True)

    class Meta:
        verbose_name = "场景记录"
        verbose_name_plural = "场景记录"
        unique_together = ('scene_id', 'env')  # 添加数据库级唯一约束，确保 scene_id + env 唯一

    @classmethod
    def get_names_by_scene_ids(cls, scene_id_list,env):
        """根据 scene_id 列表查询 name"""
        queryset = cls.objects.filter(scene_id__in=scene_id_list,env=env).values('scene_id', 'name')
        return list(queryset)


class ReportModel(models.Model):
    report_id = models.CharField(max_length=100, unique=True)  # 确保 reportId 唯一
    report_name = models.CharField(max_length=100, null=True, blank=True)
    status = models.CharField(max_length=20, null=True, blank=True)
    scene_num = models.CharField(max_length=100, null=True, blank=True)
    success_count = models.IntegerField(null=True, blank=True)
    error_count = models.IntegerField(null=True, blank=True)
    end_time = models.DateTimeField(null=True, blank=True, default=timezone.now)  # 毫秒时间戳的时间字段
    report_url = models.CharField(max_length=200, null=True, blank=True)  # 允许为空


class AppModel(models.Model):
    app_name = models.CharField(max_length=255, verbose_name="应用名")
    env = models.CharField(max_length=1, choices=ENV_CHOICES, verbose_name="看板环境")
    scene_ids = models.JSONField(verbose_name="可执行场景集", default=list)

    class Meta:
        unique_together = ('app_name', 'env')  # 确保组合唯一性

    def __str__(self):
        return self.app_name

    @classmethod
    def create_or_update_app(cls, app_name, env, scene_ids=None):
        """
        创建：app_name&env is None;
        更新：app_name&env存在;scene_ids不匹配时
        """
        if scene_ids is None:
            scene_ids = []
        obj, created = cls.objects.get_or_create(
            app_name=app_name,
            env=env,
            defaults={'scene_ids': scene_ids}
        )
        if not created and obj.scene_ids != scene_ids and scene_ids != []:
            obj.scene_ids = scene_ids
            obj.save()
        return obj

    @classmethod
    def delete_app(cls, app_name, env):
        """
        逻辑删除：将指定 app_name & env 对应的 scene_ids 置为空列表
        """
        try:
            obj = cls.objects.get(app_name=app_name, env=env)
            if obj.scene_ids != []:
                obj.scene_ids = []
                obj.save()
            return obj
        except cls.DoesNotExist:
            return None

    @classmethod
    def get_apps_empty(cls, env):
        """
        获取未关联自动化任务的服务
        """
        return cls.objects.filter(env=env, scene_ids=[]).values_list('app_name', flat=True).distinct()

    @classmethod
    def get_data(cls):
        # 获取scene_ids非空的数据（非空列表）
        queryset = cls.objects.exclude(scene_ids=[])

        # # 用defaultdict聚合数据
        # grouped_data = []
        #
        # for obj in queryset:
        #     grouped_data.append({
        #         obj.env: {
        #             "app_name": obj.app_name,
        #             "scene_ids": obj.scene_ids,
        #         }
        #     })

        return queryset

    @classmethod
    def get_scene_ids(cls, app_name, env):
        """
        获取app关联的自动化场景集
        """
        try:
            obj = cls.objects.get(app_name=app_name, env=env)
            return obj.scene_ids
        except cls.DoesNotExist:
            return []
