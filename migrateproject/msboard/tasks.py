import time
from apscheduler.schedulers.background import BackgroundScheduler
from django_apscheduler.jobstores import DjangoJobStore, register_job
from migrateproject import settings
from msboard.services.execute_service import Execute
from msboard.services.interface_service import DashboardCollection, dashboard_collection_task
from msboard.services.report_service import Reporter

scheduler = BackgroundScheduler()
scheduler.add_jobstore(DjangoJobStore(), "default")


@register_job(scheduler, "cron", minute="*/2", id='feishu_collection_task', replace_existing=True)
def feishu_collection_task():
    dashboard_collection_task(check_time=False)
    reporter = Reporter()
    reporter.collection()


# @register_job(scheduler, "cron", minute="*", id='execute_task', replace_existing=True)
# def execute_task():
#     # "初始化看板"
#     exe = Execute()
#     exe.init_task_list()
#     print("tasklist:",exe.task_list)
#     exe.execute()  # 构建即执行任务
