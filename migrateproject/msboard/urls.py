from django.urls import path
from .views import *

urlpatterns = [
    path('msboard/create/', CreateDashboardView.as_view(), name='CreateDashboardView'),
    path('msboard/interface/list', InterfaceBoardListView.as_view(), name='InterfaceBoardListView'),
    path('msboard/config_page', ConfigPageView.as_view(), name='ConfigPageView'),
    path('msboard/edit/', EditDashboardView.as_view(), name='EditDashboardView'),
    path('msboard/run/', RunView.as_view(), name='RunView'),
    path('msboard/del/', Delete.as_view(), name='Delete'),
    path('execute/build_call/', BuildCall.as_view(), name='BuildCall'),
    path('execute/list/', ExecuteListView.as_view(), name='ExecuteListView'),
    path('execute/create/', AppCreateView.as_view(), name='AppCreateView'),
    path('execute/del/', AppDeleteView.as_view(), name='AppDeleteView'),
    path('execute/config', AppConfigView.as_view(), name='AppConfigView'),
    path('debug/', DeBugView.as_view(), name='DeBugView'),
]
