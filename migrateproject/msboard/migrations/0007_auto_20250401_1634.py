# Generated by Django 3.2.25 on 2025-04-01 08:34

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('msboard', '0006_auto_20250331_1152'),
    ]

    operations = [
        migrations.CreateModel(
            name='DashboardModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255, verbose_name='名称')),
                ('scene_id', models.IntegerField(verbose_name='场景ID')),
                ('scene_type', models.CharField(choices=[('0', '接口自动化'), ('1', 'UI自动化')], max_length=1, verbose_name='场景类型')),
                ('env', models.CharField(choices=[('0', 'beta'), ('1', 'pre'), ('2', 'gray'), ('3', 'prod')], max_length=1, verbose_name='环境')),
                ('feishu_report', models.CharField(choices=[('0', '抛出报告'), ('1', '不抛出报告'), ('2', '仅抛出错误报告')], max_length=1, verbose_name='飞书报告')),
            ],
            options={
                'verbose_name': '场景记录',
                'verbose_name_plural': '场景记录',
                'unique_together': {('scene_id', 'env')},
            },
        ),
        migrations.DeleteModel(
            name='Dashboard',
        ),
    ]
