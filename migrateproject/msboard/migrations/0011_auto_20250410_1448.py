# Generated by Django 3.2.25 on 2025-04-10 06:48

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('msboard', '0010_alter_dashboardmodel_ms_env_id'),
    ]

    operations = [
        migrations.CreateModel(
            name='ReportModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('report_id', models.Char<PERSON>ield(max_length=100, unique=True)),
                ('report_name', models.CharField(blank=True, max_length=100, null=True)),
                ('status', models.CharField(blank=True, max_length=20, null=True)),
                ('scene_num', models.CharField(blank=True, max_length=100, null=True)),
                ('success_count', models.IntegerField(blank=True, null=True)),
                ('error_count', models.IntegerField(blank=True, null=True)),
                ('end_time', models.DateTimeField(blank=True, null=True)),
                ('report_url', models.Char<PERSON>ield(blank=True, max_length=200, null=True)),
            ],
        ),
        migrations.AddField(
            model_name='dashboardmodel',
            name='feishu_robot',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='机器人地址'),
        ),
        migrations.AddField(
            model_name='dashboardmodel',
            name='feishu_user',
            field=models.JSONField(blank=True, null=True, verbose_name='飞书用户列表'),
        ),
    ]
