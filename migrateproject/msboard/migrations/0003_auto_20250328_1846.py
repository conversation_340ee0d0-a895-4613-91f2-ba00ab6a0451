# Generated by Django 3.2.25 on 2025-03-28 10:46

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('msboard', '0002_dashboard_scene_type'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='dashboard',
            name='display_position',
        ),
        migrations.AddField(
            model_name='dashboard',
            name='env',
            field=models.JSONField(default=list, help_text="环境配置列表，例如：['dev', 'prod']", verbose_name='环境配置'),
        ),
        migrations.AlterField(
            model_name='dashboard',
            name='scene_type',
            field=models.CharField(choices=[('0', '接口自动化'), ('1', 'UI自动化')], default='0', max_length=10),
        ),
    ]
