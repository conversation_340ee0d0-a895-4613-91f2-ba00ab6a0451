# Generated by Django 3.2.25 on 2025-06-17 06:50

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('msboard', '0013_auto_20250422_1031'),
    ]

    operations = [
        migrations.CreateModel(
            name='AppModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('app_name', models.CharField(max_length=255, unique=True, verbose_name='应用名')),
                ('env', models.CharField(choices=[('3', 'prod'), ('1', 'pre'), ('2', 'gray'), ('0', 'beta')], max_length=1, verbose_name='看板环境')),
                ('scene_ids', models.JSONField(default=list, verbose_name='可执行场景集')),
            ],
            options={
                'unique_together': {('app_name', 'env')},
            },
        ),
    ]
