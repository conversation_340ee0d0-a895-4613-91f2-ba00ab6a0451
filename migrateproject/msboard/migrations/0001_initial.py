# Generated by Django 3.2.25 on 2025-03-28 03:04

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Dashboard',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255, verbose_name='看板名')),
                ('scene_id', models.CharField(max_length=255, unique=True, verbose_name='场景ID')),
                ('display_position', models.CharField(choices=[('ALL', 'All'), ('prod', 'Production'), ('pre', 'Pre-release'), ('beta', 'Beta')], default='0', max_length=10, verbose_name='展示位置')),
                ('feishu_management', models.JSONField(default=list, verbose_name='报告推送飞书管理')),
            ],
        ),
    ]
