from rest_framework import serializers
from .models import DashboardModel, AppModel


class DashboardBaseSerializer(serializers.ModelSerializer):
    class Meta:
        model = DashboardModel
        fields = '__all__'


class DashboardSerializer(DashboardBaseSerializer):

    def validate(self, data):
        if DashboardModel.objects.filter(scene_id=data['scene_id'], env=data['env']).exists():
            raise serializers.ValidationError("相同 scene_id 和 env 的记录已存在")
        return data


class DashboardModelDeleteSerializer(serializers.Serializer):
    scene_id = serializers.CharField()
    env = serializers.CharField()


class AppModelSerializer(serializers.Serializer):
    app_name = serializers.CharField(max_length=255)
    env = serializers.ChoiceField(choices=[choice[0] for choice in AppModel._meta.get_field('env').choices])
    scene_ids = serializers.ListField(child=serializers.IntegerField(), required=False)

class AppModelDeleteSerializer(serializers.Serializer):
    app_name = serializers.CharField(max_length=255)
    env = serializers.ChoiceField(choices=[choice[0] for choice in AppModel._meta.get_field('env').choices])

