from tools.requests_tool import RequestHandler
import time
from jsonpath import jsonpath


class ApiConfig:
    def __init__(self):
        self.BASE_URL = "http://qa.qimai.shop"
        self.HEADERS = {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN',
            'CSRF-TOKEN': '/Mwt/urWkSvZ6D9n4dM1nS5J9A0QPw4GvKEWJuvFWv9WMzM+a/jaKZU3eP/afl8RNowGyaqVDW9j7cQMbPndcG/oHmx9VM8fSVoEgMspcrg=',
            'Connection': 'keep-alive',
            'Content-Type': 'application/json;charset=UTF-8',
            'Cookie': 'jenkins-timestamper-offset=-28800000; sensorsdata2015jssdkchannel=%7B%22prop%22%3A%7B%22_sa_channel_landing_url%22%3A%22%22%7D%7D; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%22191b1a44dfce43-0e4599e184cf9c-26001151-2073600-191b1a44dfd14e3%22%2C%22first_id%22%3A%22%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%2C%22%24latest_referrer%22%3A%22%22%7D%2C%22identities%22%3A%22eyIkaWRlbnRpdHlfY29va2llX2lkIjoiMTkxYjFhNDRkZmNlNDMtMGU0NTk5ZTE4NGNmOWMtMjYwMDExNTEtMjA3MzYwMC0xOTFiMWE0NGRmZDE0ZTMifQ%3D%3D%22%2C%22history_login_id%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A%22%22%7D%2C%22%24device_id%22%3A%22191b1a44dfce43-0e4599e184cf9c-26001151-2073600-191b1a44dfd14e3%22%7D; sensors_amp_id=191b1a44dfce43-0e4599e184cf9c-26001151-2073600-191b1a44dfd14e3; nickname=2|1:0|10:1730284257|8:nickname|12:5q+V57qq5rOi|2765210a13525856b04ba5e1adbcb64ea967bf4ea70ce280beaf9534bd2db70b; username=2|1:0|10:1730284257|8:username|8:YmlqaWJv|c0278deb6771ede0da9a392393b3b11875391a4d8d0ab43e65e0ab33bf4eaa66; user_id=2|1:0|10:1730284257|7:user_id|4:NzQ5|b3f68fe4294367b69a666638801bd2c2a6aab3c759c044dc93ff49ecd22f6dc0; email=2|1:0|10:1730284257|5:email|20:YmlqaWJvQHFtYWkuY29t|cb99eec4b3877d7b87e5403899dfbe080465de514887ec7e8355cada2a0a1ba5',
            'ORGANIZATION': '100001',
            'Origin': 'http://qa.qimai.shop',
            'PROJECT': '100001100001',
            'Referer': 'http://qa.qimai.shop/',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
            'X-AUTH-TOKEN': '80664dcc-f707-453c-8b84-b90f09eceaba'
        }
        self.PATH = None
        self.BODY = None
        self.PROJECT_ID = "100001100001"

    def scenario_execute_page(self):
        self.PATH = "/api/scenario/execute/page"
        self.BODY = {"current": 1, "pageSize": 10, "sort": {"startTime": "desc"}, "keyword": "", "viewId": "",
                     "combineSearch": {"searchMode": "AND", "conditions": []}, "id": "11473919232385024", "filter": {}}
        return self

    def report_scenario_taskStep(self, execute_id: str):
        self.PATH = f"/api/report/scenario/task-step/{execute_id}?_t={ApiConfig.get_timestamp_ms()}"
        return self

    def report_share_gen(self, reportId):
        self.PATH = "/api/report/share/gen"
        self.BODY = {"reportId": reportId, "projectId": self.PROJECT_ID}
        return self

    def test_env_list(self):
        self.PATH = f"/api/test/env-list/{self.PROJECT_ID}?_t={ApiConfig.get_timestamp_ms()}"
        return self

    def scenario_get(self, scene_id):
        self.PATH = f"/api/scenario/get/{scene_id}?_t={ApiConfig.get_timestamp_ms()}"
        return self

    def scenario_run(self, scenario_config, steps, environment_id, scene_id):
        self.PATH = "/api/scenario/run"
        self.BODY = {
            "environmentId": environment_id,
            "fileParam": {"linkFileIds": [], "uploadFileIds": []},
            "grouped": "false",
            "id": scene_id,
            "projectId": self.PROJECT_ID,
            "reportId": str(ApiConfig.get_timestamp_ms()) + "00000",
            "scenarioConfig": scenario_config,
            "stepDetails": {},
            "stepFileParam": {},
            "steps": steps
        }
        return self

    @staticmethod
    def get_timestamp_ms():
        return int(time.time() * 1000)


class ApiClient:
    def __init__(self, config: ApiConfig):
        self.config = config
        self.client = RequestHandler(base_url=self.config.BASE_URL, headers=self.config.HEADERS)

    def get_new_history_info(self, sceneId):
        body = self.config.scenario_execute_page().BODY
        body["id"] = sceneId  # 场景ID
        res = self.client.post(endpoint=self.config.PATH, json=body)
        new_history = jsonpath(res, "$.data.list[0]")[0] or None
        executeId = new_history.get("id", None)
        return new_history, executeId

    def get_report(self, executeId):
        path = self.config.report_scenario_taskStep(executeId).PATH
        res = self.client.get(endpoint=path)
        return res or None, jsonpath(res, "$.data.id")[0] or None  # report,report_id

    def get_share_path(self, reportId):
        path = self.config.report_share_gen(reportId).PATH
        body = self.config.BODY
        res = self.client.post(endpoint=path, json=body)
        sharePath = jsonpath(res, "$.data.shareUrl")[0]
        return f"{self.config.BASE_URL}/#/share/shareReportScenario{sharePath}"

    def get_test_env_list(self):
        path = self.config.test_env_list().PATH
        res = self.client.get(endpoint=path)
        result_list = [{item["name"]: item["id"]} for item in res.get("data", [])]
        return result_list

    def get_scenario_get(self, scene_id):
        path = self.config.scenario_get(scene_id).PATH
        res = self.client.get(endpoint=path)
        steps = jsonpath(res, "$.data.steps").pop()
        scenario_config = jsonpath(res, "$.data.scenarioConfig").pop()
        return steps or None, scenario_config or None

    def scenario_run(self, scenario_config, steps, environment_id, scene_id):
        path = self.config.scenario_run(scenario_config, steps, environment_id, scene_id).PATH
        body = self.config.BODY
        res = self.client.post(endpoint=path, json=body)
        return res
