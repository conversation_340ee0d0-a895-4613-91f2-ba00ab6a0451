"""
**************************************
*  <AUTHOR>   毕纪波
*  @Time    ：   2025/5/26 10:54
*  @Project :   migrateproject
*  @FileName:   auto_execute_service.py
*  @description: 
**************************************
"""
import queue
from django.shortcuts import get_object_or_404
from migrateproject import settings
from msboard.models import AppModel, DashboardModel
from msboard.services.api_client import ApiClient, ApiConfig
from msboard.services.base_class import SingletonMeta
from msboard.services.interface_service import transform_data, dashboard_collection_task


class Execute(metaclass=SingletonMeta):
    """
    时序：
    put_task_queue(加入应用队列)->_put_task_list(应用绑定压测场景)->set_task_list(评估压测场景是否可执行)->execute(执行任务)
    """

    def __init__(self):
        self.task_queue = queue.Queue()  # 创建服务队列
        self.task_list = []  # 创建自动化执行任务集合
        self.CATERING_SCENEID_GROUP = ["23183597808730112", "615984210059267", "621670746759170", "1024384060301315",
                                       "6862464385859584"]
        self.PUBLIC_SCENEID_GROUP = ["5774308651655168", "5657571440549888", "57439810284961792"]
        self.client = ApiClient(ApiConfig())

    def put_task_queue(self, app_name: str | list, env: str) -> None:
        """
        将运维返回的构建成功应用加入队列&入库
        """
        if app_name and env:
            if isinstance(app_name, list) and app_name:
                for app in app_name:
                    self.task_queue.put({"app_name": app, "env": env})  # 加入队列
                    AppModel.create_or_update_app(app, env)  # 入库
            if isinstance(app_name, str) and app_name:
                self.task_queue.put({"app_name": app_name, "env": env})  # 加入队列
                AppModel.create_or_update_app(app_name, env)  # 入库
        return

    def _bind_scene_id(self, task_queue) -> None:
        """
        关联应用与case场景，并加入到任务集合
        """
        app_name = task_queue.get("app_name", None)
        env = task_queue.get("env", None)
        if app_name and env:
            scene_ids = AppModel.get_scene_ids(app_name=app_name, env=env)
            if not scene_ids:
                return
            for scene_id in scene_ids:
                self.task_list.append({"scene_id": str(scene_id), "env": str(env), "runnable": False, "been_run": 0})
        return

    @staticmethod
    def _set_task_runnable(task) -> bool:
        """
        判断task是否可运行，并将可运行的task["runnable"]设置为True
        """
        scene_id = task.get("scene_id", None)
        env = task.get("env", None)
        # 提取指定环境的看板数据
        if not settings.INTERFACE_BOARD_DATA:
            return False
        checked = Execute._check_task_runnable(scene_id, env)
        if checked:
            task["runnable"] = True
            return True
        task["runnable"] = False
        return False

    @staticmethod
    def _check_task_runnable(scene_id, env) -> bool:
        """
        看板中存在且exec_status != "RUNNING"，认定为可执行
        看板中存在且exec_status == "RUNNING"，认定为不可执行
        看板数据为空 or 看板数据中找不到相关记录，认定为可执行
        """
        # 提取指定环境的看板数据
        board_datas = transform_data(settings.INTERFACE_BOARD_DATA)  # 获取看板
        board_env_datas = board_datas.get(env, None)  # 指定某一环境的看板
        # 检查任务是否能运行
        if not board_env_datas:
            return True
        for data in board_env_datas:
            id = data.get("scene_id", None)
            exec_status = data.get("exec_status", None)
            print(f"id:{id},exec_status:{exec_status}")
            if scene_id == id and exec_status != "RUNNING":
                return True
            elif scene_id == id and exec_status == "RUNNING":
                return False
        return True

    def _check_group_runnable(self, task, group) -> bool:
        """
        判断task是否和组内的case有执行冲突
        """
        scene_id = task.get("scene_id", None)
        env = task.get("env", None)
        if scene_id and (scene_id in group):
            for sid in group:
                check_result = self._check_task_runnable(sid, env)
                if not check_result:
                    return False
        return True

    def init_task_list(self) -> None:
        """
        判断队列内是否有数据，再判断库里对应服务是否绑定测试场景，如果绑定，加入到执行任务队列
        """
        if not self.task_queue.empty():
            for _ in range(self.task_queue.qsize()):
                app_data = self.task_queue.get()
                self._bind_scene_id(app_data)
        return

    def execute(self) -> None:
        """
        执行入口
        """
        self._update_task_list()  #
        if not self.task_list:
            return
        if all(item.get("been_run") == 1 for item in self.task_list):  # 判断列表中的所有been_run是否都执行了
            self.task_list.clear()
        return

    def _update_task_list(self) -> None:
        """
        更新task的been_run属性
        """
        for task in self.task_list:
            scene_id = task["scene_id"]
            env = task["env"]
            dashboard_collection_task(False)  # 更新一次看板
            if not (self._check_group_runnable(task, self.CATERING_SCENEID_GROUP)
                    and self._check_group_runnable(task, self.PUBLIC_SCENEID_GROUP)  # 判断task是否存在组关系,且会不会存在互相影响
                    and task["been_run"] == 0):
                continue
            if not self._set_task_runnable(task):
                continue
            run_result = self._run_case(scene_id, env)
            if not run_result:
                continue
            task["been_run"] = 1
        return

    def _run_case(self, scene_id, env):
        try:
            ms_env_id = get_object_or_404(DashboardModel, scene_id=scene_id, env=env).ms_env_id
            steps, scenario_config = self.client.get_scenario_get(scene_id)
            self.client.scenario_run(
                scenario_config=scenario_config,
                steps=steps,
                environment_id=ms_env_id,
                scene_id=scene_id
            )
            return True
        except Exception as e:
            print(e.__traceback__)
            return False

    @classmethod
    def get_scene_list_by_env(cls, env):
        if not settings.INTERFACE_BOARD_DATA:
            return []
        board_datas = transform_data(settings.INTERFACE_BOARD_DATA)
        board_datas = board_datas.get(env, [])
        scene_list = []
        if board_datas:
            scene_list = [{board.get("scene_id"): board.get("name")} for board in board_datas]
        if scene_list:
            return scene_list
        return []
