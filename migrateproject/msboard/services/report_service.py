from migrateproject import settings
from msboard.models import ReportModel
from tools.feishu_tool import FeiShuTool


class Reporter:
    REPORT_TYPE_THROW = '0'
    REPORT_TYPE_NO_THROW = '1'
    REPORT_TYPE_ERROR_ONLY = '2'

    def __init__(self):
        self.feishu_tool = FeiShuTool.get_instance()

    def _report_to_list(self):
        """
        将列表包对象的结构转为列表包字典结构
        """
        if settings.INTERFACE_BOARD_DATA:
            return [list(d.values())[0] for d in settings.INTERFACE_BOARD_DATA]

    def _save_report_to_db(self, report):
        ReportModel.objects.create(
            report_id=report.get("report_id"),
            report_name=report.get("name"),
            status=report.get("status"),
            scene_num=report.get("scene_num"),
            success_count=report.get("success_count"),
            error_count=report.get("error_count"),
            end_time=report.get("end_time"),
            report_url=report.get("report_url")
        )

    def _handle_feishu_report(self, report):
        report_type = report.get("feishu_report")

        if report_type == self.REPORT_TYPE_THROW:
            self.feishu_tool.call(report)
        elif report_type == self.REPORT_TYPE_ERROR_ONLY:
            error_count = report.get("error_count", 0)
            if error_count > 0:
                self.feishu_tool.call(report)

    def collection(self):
        report_list = self._report_to_list()
        if report_list:
            for report in report_list:
                exec_status = report.get("exec_status")
                report_id = report.get("report_id")
                if exec_status == "COMPLETED" and report_id:
                    if not ReportModel.objects.filter(report_id=report_id).exists():
                        self._handle_feishu_report(report)
                        self._save_report_to_db(report)
