"""
**************************************
*  <AUTHOR>   毕纪波
*  @Time    ：   2025/6/25 17:24
*  @Project :   migrateproject
*  @FileName:   base_class.py
*  @description: 
**************************************
"""


class SingletonMeta(type):
    """元类：用于创建单例类"""
    _instances = {}

    def __call__(cls, *args, **kwargs):
        if cls not in cls._instances:
            # 创建并缓存实例
            cls._instances[cls] = super(SingletonMeta, cls).__call__(*args, **kwargs)
        return cls._instances[cls]


class Demo(metaclass=SingletonMeta):
    def __init__(self, name):
        self.name = name

    @classmethod
    def get_name(cls):
        return 1

if __name__ == '__main__':
    print(Demo.get_name())