'''
@-*- coding: utf-8 -*-
@ python：python 3.10
@ 文件名：services.py
@ 创建人员：毕纪波
@ 创建时间：2025-03-21
'''
import os
import time
from datetime import datetime
from typing import Dict, Optional, Tuple, Any
from django.apps import apps
from jsonpath import jsonpath
from migrateproject import settings
from msboard.services.api_client import ApiClient, ApiConfig
from concurrent.futures import ThreadPoolExecutor, as_completed


class DashboardData:
    def __init__(self, sceneId, env):
        self.sceneId = sceneId
        self.env_map: Dict = settings.ENV_MAP.get(env, {})
        self.client = ApiClient(ApiConfig())
        # 初始化属性
        self.scene_num: Optional[int] = None
        self.success_count: Optional[int] = None
        self.error_count: Optional[int] = None
        self.end_time: Optional[str] = None
        self.report_url: Optional[str] = None
        self.status: Optional[str] = None
        self.exec_status: Optional[str] = None
        self.report_id: Optional[str] = None
        self.feishu_report: Optional[str] = None
        self.ms_env_id: Optional[int] = None
        self.test = env

    def set_data(self):
        try:
            execute_history, execute_id = self._get_execute_id()
            if not execute_id:
                return
            report, report_id = self._get_report_data(execute_id)
            if not report or not report_id:
                return
            if not self._validate_env(report):
                return
            self._parse_report_data(report)
            self._generate_report_url(report_id)
            self.exec_status = self._get_jsonpath_value(execute_history, "$.execStatus")
            self.status = self._get_jsonpath_value(execute_history, "$.status")
        except Exception as e:
            # 添加日志记录实际异常
            print(f"Error setting dashboard data: {str(e)}")
            # 可以考虑重新抛出异常或记录到日志系统

    def _get_execute_id(self) -> tuple[Any, Any] | None:
        """获取执行ID"""
        try:
            execute_history, execute_id = self.client.get_new_history_info(self.sceneId)
            return execute_history, execute_id
        except (AttributeError, ValueError) as e:
            print(f"Error getting execute ID: {str(e)}")
            return None

    def _get_report_data(self, execute_id: str) -> tuple:
        """获取测试报告数据"""
        try:
            report, report_id = self.client.get_report(execute_id)
            return (report, report_id)
        except Exception as e:
            print(f"Error getting report: {str(e)}")
            return (None, None)

    def _validate_env(self, report: dict) -> bool:
        """验证环境匹配"""
        env_name = self._get_jsonpath_value(report, "$.data.environmentName")
        return env_name in self.env_map if env_name else False

    def _parse_report_data(self, report: dict) -> None:
        """解析报告数据"""
        self.scene_num = self._get_jsonpath_value(report, "$.data.stepTotal", 0)
        self.error_count = self._get_jsonpath_value(report, "$.data.stepErrorCount", 0)
        self.success_count = self.scene_num - self.error_count
        end_timestamp = self._get_jsonpath_value(report, "$.data.endTime")
        if end_timestamp:
            self.end_time = datetime.fromtimestamp(end_timestamp / 1000).strftime('%Y-%m-%d %H:%M:%S')
        self.report_id = self._get_jsonpath_value(report, "$.data.id")

    def _generate_report_url(self, report_id: str) -> None:
        """生成报告URL"""
        try:
            self.report_url = self.client.get_share_path(report_id)
        except Exception as e:
            print(f"Error generating report URL: {str(e)}")
            self.report_url = "URL Generation Failed"

    @staticmethod
    def _get_jsonpath_value(data: dict, expr: str, default=None):
        """安全的JSONPath值获取"""
        result = jsonpath(data, expr)
        return result[0] if result else default

    @staticmethod
    def calculate_days(target_time_str: Optional[str]) -> int:
        """
        计算当前时间与指定时间的相差天数
        规则：不足1天返回0，大于等于1天返回整数天数
        :param target_time_str: 时间字符串，格式需为"%Y-%m-%d %H:%M:%S"
        :return: 相差天数（永远非负）
        """
        if not target_time_str:
            return 0
        try:
            # 解析目标时间
            target_time = datetime.strptime(target_time_str, "%Y-%m-%d %H:%M:%S")
        except (ValueError, TypeError):
            return 0
        # 获取当前时间
        now = datetime.now()
        # 处理未来时间
        if now <= target_time:
            return 0
        # 计算时间差
        delta = now - target_time
        # 返回整数天数（delta.days可能为0）
        return max(delta.days, 0)


class DashboardRecord:
    """更新单个场景的数据记录"""

    def __init__(self, model_instance, dashboard_data):
        self.model = model_instance
        self.data = dashboard_data
        self.error: Optional[str] = None

    @property
    def key(self) -> Tuple[int, str]:
        """获取唯一标识键"""
        return (self.model.scene_id, self.model.env)

    def refresh(self):
        """更新报告数据"""
        try:
            self.data.set_data()
            self.error = None
            self.data.on_off = False
        except Exception as e:
            self.error = str(e)

    def to_dict(self):
        """序列化输出"""
        return {
            "scene_id": self.model.scene_id,
            "report_id": self.data.report_id,
            "env": self.model.get_env_display(),
            "name": self.model.name,
            "scene_num": self.data.scene_num,
            "scene_type": self.model.get_scene_type_display(),
            "success_count": self.data.success_count,
            "error_count": self.data.error_count,
            "report_url": self.data.report_url,
            "end_time": self.data.end_time,
            "status": self.data.status,
            "exec_status": self.data.exec_status,
            "ms_env_id": self.model.ms_env_id,
            "feishu_report": self.model.feishu_report,
            "feishu_robot": self.model.feishu_robot,
            "feishu_user": self.model.feishu_user,
            "error": self.error
        }


class DashboardCollection:
    """更新全量场景的数据记录"""

    def __init__(self):
        self.records: Dict[Tuple[int, str], DashboardRecord] = {}
        self.FIELDS_TO_UPDATE = ["name", "env", "ms_env_id", "feishu_report", "feishu_robot", "feishu_user"]

    def initialize(self):
        """初始化加载所有数据（支持多线程）"""
        DashboardModel = apps.get_model('msboard', 'DashboardModel')
        instances = list(DashboardModel.objects.filter(scene_type=0))
        self.records.clear()

        def create_and_refresh(instance):
            env_display = instance.get_env_display()
            dashboard_data = DashboardData(sceneId=instance.scene_id, env=env_display)
            record = DashboardRecord(instance, dashboard_data)
            record.refresh()
            return record.key, record

        with ThreadPoolExecutor(max_workers=4) as executor:
            future_to_record = {executor.submit(create_and_refresh, instance): instance for instance in instances}
            for future in as_completed(future_to_record):
                try:
                    key, record = future.result()
                    self.records[key] = record
                except Exception as e:
                    print(f"Error processing instance: {str(e)}")

    def get_all_reports(self):
        """获取所有报告数据并更新到 settings.INTERFACE_BOARD_DATA"""
        # 新数据列表
        data_tmp = [{key: value.to_dict()} for key, value in self.records.items()]

        if settings.INTERFACE_BOARD_DATA is None:
            settings.INTERFACE_BOARD_DATA = data_tmp
            return
        # 构建原始数据的字典方便查找和更新
        existing_data = {
            list(item.keys())[0]: list(item.values())[0]
            for item in settings.INTERFACE_BOARD_DATA
        }
        # 遍历新数据进行合并（仅在 exec_status 为 COMPLETED 时替换）
        for item in data_tmp:
            key, value = list(item.items())[0]
            # 更新看板编辑数据
            for field in self.FIELDS_TO_UPDATE:
                if existing_data[key][field] != value[field]:
                    existing_data[key][field] = value[field]
            if value.get("exec_status") is not None:
                existing_data[key] = value  # 替换旧数据
        # 将合并后的数据重新转回列表格式
        settings.INTERFACE_BOARD_DATA = [{k: v} for k, v in existing_data.items()]
        return

    @staticmethod
    def add_record(scene_id: int, env: str):
        """静态方法：仅从数据库读取数据，添加到 settings.INTERFACE_BOARD_DATA"""
        DashboardModel = apps.get_model('msboard', 'DashboardModel')
        instance = DashboardModel.objects.filter(scene_id=scene_id, scene_type=0).first()
        if not instance:
            raise ValueError(f"Scene ID {scene_id} 不存在")

        env_display = instance.env
        if env != env_display:
            raise ValueError(f"环境不匹配：期望 {env_display}，实际传入 {env}")

        dashboard_data = DashboardData(sceneId=scene_id, env=env)
        record = DashboardRecord(instance, dashboard_data)
        record.refresh()
        key = record.key

        new_entry = {key: record.to_dict()}

        if settings.INTERFACE_BOARD_DATA is None:
            settings.INTERFACE_BOARD_DATA = [new_entry]
        else:
            settings.INTERFACE_BOARD_DATA = [
                item for item in settings.INTERFACE_BOARD_DATA if list(item.keys())[0] != key
            ]
            settings.INTERFACE_BOARD_DATA.append(new_entry)

    @staticmethod
    def remove_record(scene_id: int, env: str):
        """静态方法：仅从 settings.INTERFACE_BOARD_DATA 中删除记录"""
        key = (scene_id, env)
        if settings.INTERFACE_BOARD_DATA:
            settings.INTERFACE_BOARD_DATA = [
                item for item in settings.INTERFACE_BOARD_DATA if list(item.keys())[0] != key
            ]


def transform_data(original_list):
    result = {}
    for item in original_list:
        # 提取原始字典的key和value
        original_key, value_dict = next(iter(item.items()))
        # 获取新字典的key（元组的第二个元素）
        new_key = original_key[1]
        # 处理重复key的聚合
        if new_key in result:
            # 如果key已存在，将当前值添加到已有集合中
            result[new_key].append(value_dict)
        else:
            # 如果key不存在，创建新的集合
            result[new_key] = [value_dict]
    return result

def dashboard_collection_task(check_time=False):
    if check_time:
        current_time = time.time()
        # 如果 CURRENT_TIME 未设置，或者距离上次调用超过60秒，则执行任务
        if not settings.CURRENT_TIME or (current_time - settings.CURRENT_TIME) > 20:
            settings.CURRENT_TIME = current_time  # 更新 CURRENT_TIME
        else:
            return
    collection = DashboardCollection()
    collection.initialize()
    collection.get_all_reports()
    return