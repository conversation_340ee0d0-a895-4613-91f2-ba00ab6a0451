'''
@-*- coding: utf-8 -*-
@ python：python 3.10
@ 文件名：feishu_tool.py
@ 创建人员：毕纪波
@ 创建时间：2024-10-30
'''
import requests


class FeiShuTool:
    _instance = None
    _headers = {'content-type': 'application/json'}

    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            cls._instance = super().__new__(cls, *args, **kwargs)
        return cls._instance

    @classmethod
    def get_instance(cls):
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance

    def call(self, report_obj):
        content = self._build_content(report_obj)
        payload = {"msg_type": "post", "content": content}
        try:
            response = requests.post(url=report_obj["feishu_robot"], json=payload, headers=self._headers)
            response.raise_for_status()
        except requests.exceptions.RequestException as e:
            print(f"Failed to send message: {e}")
        else:
            print("Message sent successfully.")
        return

    def _build_content(self, report_obj):
        content = [
            {"tag": "text", "text": f"\n执行环境：{report_obj['env']}"},
            {"tag": "text",
             "text": f"\n完成时间：{report_obj['end_time']}"},
            {"tag": "text", "text": f"\n场景数：{report_obj['scene_num']}"},
            {"tag": "text", "text": f"\n通过数：{report_obj['success_count']}"},
            {"tag": "text", "text": f"\n失败数：{report_obj['error_count']}"},
            {"tag": "text", "text": f"\n点击查看："},
            {"tag": "a", "text": "报告详情", "href": report_obj['report_url']},
            {"tag": "text", "text": "\n"},
        ]
        feishu_user = report_obj["feishu_user"]
        if feishu_user:
            for user_id in feishu_user:
                content.append({"tag": "at", "user_id": user_id})
        return {
            "post": {
                "zh_cn": {
                    "title": f"{report_obj['name']}",
                    "content": [content]
                }
            }
        }

