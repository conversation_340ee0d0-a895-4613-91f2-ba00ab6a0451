'''
@-*- coding: utf-8 -*-
@ python：python 3.10
@ 文件名：requests_tool.py
@ 创建人员：毕纪波
@ 创建时间：2024-10-30
'''
import time
import requests
from requests.exceptions import RequestException


class RequestHandler:
    def __init__(self, base_url=None, headers=None, timeout=120, max_retries=1, retry_delay=100):
        """
        初始化请求处理器
        :param base_url: 基础URL
        :param headers: 请求头，默认为空
        :param timeout: 请求超时时间，默认10秒
        :param max_retries: 最大重试次数，默认5次
        :param retry_delay: 重试等待时间（秒），默认10秒
        """
        self.base_url = base_url if base_url else ""
        self.headers = headers if headers else {}
        self.timeout = timeout
        self.max_retries = max_retries
        self.retry_delay = retry_delay

    def _request_with_retry(self, method, url, **kwargs):
        """
        带重试机制的请求
        :param method: HTTP方法 ('get', 'post', 等)
        :param url: 请求URL
        :param kwargs: 请求参数
        :return: 请求结果的JSON格式或错误信息
        """
        retries = 0
        while retries < self.max_retries:
            try:
                response = requests.request(method, url, timeout=self.timeout, **kwargs)
                if response.status_code == 200:
                    return response.json()
                else:
                    print(f"请求失败，状态码: {response.status_code}，重试中...({retries + 1}/{self.max_retries})")
            except RequestException as e:
                print(f"请求异常: {e}，重试中...({retries + 1}/{self.max_retries})")
            retries += 1
            time.sleep(self.retry_delay)
        print("请求重试已达上限，操作失败。")
        return None

    def get(self, endpoint, params=None, headers=None):
        """
        发送GET请求
        :param endpoint: API接口路径
        :param params: URL参数
        :param headers: 请求头
        :return: 请求结果的JSON格式或错误信息
        """
        url = self.base_url + endpoint
        return self._request_with_retry('get', url, params=params, headers={**self.headers, **(headers or {})})

    def post(self, endpoint, data=None, json=None, headers=None):
        """
        发送POST请求
        :param endpoint: API接口路径
        :param data: 表单数据
        :param json: JSON数据
        :param headers: 请求头
        :return: 请求结果的JSON格式或错误信息
        """
        url = self.base_url + endpoint
        return self._request_with_retry('post', url, data=data, json=json, headers={**self.headers, **(headers or {})})

    def set_headers(self, headers):
        """
        设置默认请求头
        :param headers: 请求头字典
        """
        self.headers = headers
        return self

    def set_timeout(self, timeout):
        """
        设置请求超时时间
        :param timeout: 超时时间（秒）
        """
        self.timeout = timeout
        return self
